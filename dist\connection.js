/**
 * MySQL 连接管理与连接池
 *
 * 高级的 MySQL 连接池管理系统，具有健康监控、自动重连和性能优化功能。
 * 提供企业级的连接处理，包括统计跟踪和优雅关闭功能。
 *
 * @fileoverview 带健康监控的 MySQL 连接池实现
 * <AUTHOR>
 * @since 1.0.0
 */
import { createPool } from 'mysql2/promise';
import { StringConstants, DefaultConfig } from './constants.js';
/**
 * 连接池类
 *
 * 使用连接池管理 MySQL 数据库连接，具有自动健康监控、
 * 预连接预热和全面的统计跟踪功能。
 *
 * 功能特性：
 * - 自动连接池管理
 * - 可配置间隔的健康检查监控
 * - 为性能预创建最小连接数
 * - 连接统计和性能指标
 * - 带适当资源清理的优雅关闭
 * - 可配置安全设置的 SSL/TLS 支持
 *
 * @class ConnectionPool
 * @since 1.0.0
 */
export class ConnectionPool {
    /** 数据库配置设置 */
    config;
    /** MySQL 连接池实例 */
    pool = null;
    /** 健康检查监控间隔计时器 */
    healthCheckInterval = null;
    /** 优雅终止的关闭事件标志 */
    shutdownEvent = false;
    /** 连接池性能统计 */
    connectionStats = {
        pool_hits: 0,
        pool_waits: 0
    };
    /**
     * 连接池构造函数
     *
     * 使用提供的数据库配置初始化连接池。
     * 连接池在第一次连接请求时延迟创建。
     *
     * @constructor
     * @param {DatabaseConfig} config - 数据库连接配置
     */
    constructor(config) {
        this.config = config;
    }
    /**
     * 初始化连接池
     *
     * 创建和配置带有安全设置的MySQL连接池，预热连接，
     * 并启动健康监控。此方法是幂等的，可以安全地多次调用。
     *
     * @public
     * @returns {Promise<void>} 当连接池初始化完成时解析的Promise
     * @throws {Error} 当连接池创建或初始化失败时抛出
     *
     * @example
     * await connectionPool.initialize();
     */
    async initialize() {
        // 如果连接池已存在则跳过初始化
        if (this.pool) {
            return;
        }
        try {
            // 配置带有安全和性能设置的连接池
            const poolConfig = {
                host: this.config.host,
                port: this.config.port,
                user: this.config.user,
                password: this.config.password,
                database: this.config.database,
                connectionLimit: this.config.connectionLimit,
                connectTimeout: this.config.connectTimeout * 1000,
                charset: StringConstants.CHARSET,
                multipleStatements: false, // 安全：禁用多语句以防止SQL注入
                ssl: this.config.sslEnabled ? {} : undefined
            };
            // 创建连接池
            this.pool = createPool(poolConfig);
            // 预创建最小连接数以获得更好的初始性能
            await this.preCreateConnections();
            // 启动定期健康监控
            this.startHealthCheck();
        }
        catch (error) {
            throw new Error(`${StringConstants.MSG_FAILED_TO_INIT_POOL} ${error}`);
        }
    }
    /**
     * 预创建最小连接数
     *
     * 创建配置中指定的最小连接数，通过避免首次请求时的
     * 连接创建延迟来提高初始性能。
     *
     * @private
     * @returns {Promise<void>} 当连接预创建完成时解析的Promise
     */
    async preCreateConnections() {
        if (!this.pool)
            return;
        try {
            // 为最小连接数创建Promise
            const promises = [];
            for (let i = 0; i < this.config.minConnections; i++) {
                promises.push(this.pool.getConnection());
            }
            // 等待所有连接创建完成
            const connections = await Promise.all(promises);
            // 将连接释放回连接池以供重用
            connections.forEach(conn => conn.release());
        }
        catch (error) {
            // 预创建失败对整体功能不是关键的
            console.warn('警告：预创建连接失败:', error);
        }
    }
    /**
     * 启动健康检查监控
     *
     * 启动定期健康检查以确保连接池保持健康和响应。
     * 在启动新的健康检查间隔之前清除任何现有的健康检查间隔。
     *
     * @private
     */
    startHealthCheck() {
        // 如果存在则清除现有的健康检查间隔
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
        }
        // 启动新的健康检查间隔
        this.healthCheckInterval = setInterval(() => {
            this.performHealthCheck();
        }, DefaultConfig.HEALTH_CHECK_INTERVAL * 1000);
    }
    /**
     * 执行健康检查
     *
     * 执行简单查询以验证连接池是否正常工作。
     * 失败会被记录但不会影响正常操作。
     *
     * @private
     * @returns {Promise<void>} 当健康检查完成时解析的Promise
     */
    async performHealthCheck() {
        if (!this.pool)
            return;
        try {
            // 获取连接并执行简单测试查询
            const connection = await this.pool.getConnection();
            await connection.execute('SELECT 1');
            connection.release();
        }
        catch (error) {
            // 健康检查失败会被记录但不是关键的
            console.warn('健康检查失败:', error);
        }
    }
    /**
     * 获取数据库连接
     *
     * 从连接池中检索连接，具有自动初始化和性能跟踪功能。
     * 测量连接获取时间并更新监控统计信息。
     *
     * @public
     * @returns {Promise<PoolConnection>} 解析为数据库连接的Promise
     * @throws {Error} 当连接池初始化失败或无法获取连接时抛出
     *
     * @example
     * const connection = await pool.getConnection();
     * try {
     *   const [rows] = await connection.execute('SELECT * FROM users');
     *   return rows;
     * } finally {
     *   connection.release();
     * }
     */
    async getConnection() {
        // 如果尚未完成则初始化连接池
        if (!this.pool) {
            await this.initialize();
        }
        // 验证连接池是否成功初始化
        if (!this.pool) {
            throw new Error('连接池未初始化');
        }
        // 跟踪连接获取时间以进行性能监控
        const startTime = Date.now();
        const connection = await this.pool.getConnection();
        const waitTime = Date.now() - startTime;
        // 根据等待时间更新连接统计信息
        if (waitTime > 100) { // 超过100ms表示连接池压力
            this.connectionStats[StringConstants.FIELD_POOL_WAITS]++;
        }
        else {
            this.connectionStats[StringConstants.FIELD_POOL_HITS]++;
        }
        return connection;
    }
    /**
     * 获取连接池统计信息
     *
     * 返回关于连接池的综合统计信息，包括配置、性能指标
     * 和健康状态，用于监控和调试目的。
     *
     * @public
     * @returns {ConnectionPoolStats} 连接池统计信息和配置信息
     *
     * @example
     * const stats = pool.getStats();
     * console.log(`连接池命中: ${stats.connection_stats.pool_hits}`);
     */
    getStats() {
        if (!this.pool) {
            return { [StringConstants.STATUS_KEY]: StringConstants.STATUS_NOT_INITIALIZED };
        }
        // 返回可用的连接池统计信息和配置
        // 注意：在生产实现中，我们会访问更多连接池内部信息
        return {
            [StringConstants.FIELD_POOL_NAME]: StringConstants.POOL_NAME,
            [StringConstants.FIELD_POOL_SIZE]: this.config.connectionLimit,
            [StringConstants.FIELD_CONNECTION_STATS]: { ...this.connectionStats },
            [StringConstants.FIELD_HEALTH_CHECK_ACTIVE]: !!this.healthCheckInterval
        };
    }
    /**
     * 关闭连接池
     *
     * 执行连接池的优雅关闭，包括停止健康检查、
     * 关闭所有连接和清理资源。应在应用程序关闭时调用。
     *
     * @public
     * @returns {Promise<void>} 当连接池完全关闭时解析的Promise
     *
     * @example
     * // 优雅关闭
     * await pool.close();
     */
    async close() {
        // 设置关闭标志以防止新操作
        this.shutdownEvent = true;
        // 停止健康检查监控
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
        }
        // 关闭连接池并释放所有连接
        if (this.pool) {
            await this.pool.end();
            this.pool = null;
        }
    }
}
//# sourceMappingURL=connection.js.map