import { MySQLManager } from '../src/mysqlManager.js';

// Mock FastMCP
jest.mock('fastmcp');

describe('MySQL MCP Tools Integration Tests', () => {
  let mysqlManager: MySQLManager;
  let mockExecuteQuery: jest.SpyInstance<Promise<unknown>, [string, unknown[]?] >;

  beforeEach(() => {
    mysqlManager = new MySQLManager();
    mockExecuteQuery = jest.spyOn(mysqlManager, 'executeQuery');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('mysql_query tool', () => {
    it('should execute simple SELECT query', async () => {
      const mockResult = [{ id: 1, name: 'test' }];
      mockExecuteQuery.mockResolvedValue(mockResult);

      const result = await mysqlManager.executeQuery('SELECT * FROM users LIMIT 1');
      
      expect(mockExecuteQuery).toHaveBeenCalledWith('SELECT * FROM users LIMIT 1');
      expect(result).toEqual(mockResult);
    });

    it('should execute parameterized query', async () => {
      const mockResult = [{ id: 1, name: 'John' }];
      mockExecuteQuery.mockResolvedValue(mockResult);

      const query = 'SELECT * FROM users WHERE id = ?';
      const params = [1];
      const result = await mysqlManager.executeQuery(query, params);
      
      expect(mockExecuteQuery).toHaveBeenCalledWith(query, params);
      expect(result).toEqual(mockResult);
    });

    it('should handle query errors gracefully', async () => {
      const error = new Error('Table does not exist');
      mockExecuteQuery.mockRejectedValue(error);

      await expect(mysqlManager.executeQuery('SELECT * FROM nonexistent')).rejects.toThrow();
    });
  });

  describe('mysql_show_tables tool', () => {
    it('should return list of tables', async () => {
      const mockResult = [
        { 'Tables_in_test_db': 'users' },
        { 'Tables_in_test_db': 'products' }
      ];
      mockExecuteQuery.mockResolvedValue(mockResult);

      const result = await mysqlManager.executeQuery('SHOW TABLES');
      
      expect(mockExecuteQuery).toHaveBeenCalledWith('SHOW TABLES');
      expect(result).toEqual(mockResult);
    });
  });

  describe('mysql_describe_table tool', () => {
    it('should return table schema', async () => {
      const mockResult = [
        {
          COLUMN_NAME: 'id',
          DATA_TYPE: 'int',
          IS_NULLABLE: 'NO',
          COLUMN_DEFAULT: null,
          COLUMN_KEY: 'PRI',
          EXTRA: 'auto_increment',
          COLUMN_COMMENT: ''
        },
        {
          COLUMN_NAME: 'name',
          DATA_TYPE: 'varchar',
          IS_NULLABLE: 'NO',
          COLUMN_DEFAULT: null,
          COLUMN_KEY: '',
          EXTRA: '',
          COLUMN_COMMENT: ''
        }
      ];

      // Mock the private method
      jest.spyOn(mysqlManager as unknown as { getTableSchemaCached: (tableName: string) => Promise<unknown> }, 'getTableSchemaCached').mockResolvedValue(mockResult);
      jest.spyOn(mysqlManager as unknown as { validateTableName: (tableName: string) => void }, 'validateTableName').mockReturnValue(undefined);

      const result = await (mysqlManager as unknown as { getTableSchemaCached: (tableName: string) => Promise<unknown> })['getTableSchemaCached']('users');
      
      expect(result).toEqual(mockResult);
    });
  });

  describe('mysql_select_data tool', () => {
    it('should select data with default columns', async () => {
      const mockResult = [
        { id: 1, name: 'John', email: '<EMAIL>' },
        { id: 2, name: 'Jane', email: '<EMAIL>' }
      ];
      mockExecuteQuery.mockResolvedValue(mockResult);

      const query = 'SELECT * FROM `users`';
      const result = await mysqlManager.executeQuery(query);
      
      expect(mockExecuteQuery).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockResult);
    });

    it('should select data with specific columns', async () => {
      const mockResult = [
        { id: 1, name: 'John' },
        { id: 2, name: 'Jane' }
      ];
      mockExecuteQuery.mockResolvedValue(mockResult);

      const query = 'SELECT id, name FROM `users`';
      const result = await mysqlManager.executeQuery(query);
      
      expect(mockExecuteQuery).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockResult);
    });

    it('should select data with WHERE clause', async () => {
      const mockResult = [{ id: 1, name: 'John', email: '<EMAIL>' }];
      mockExecuteQuery.mockResolvedValue(mockResult);

      const query = 'SELECT * FROM `users` WHERE id = 1';
      const result = await mysqlManager.executeQuery(query);
      
      expect(mockExecuteQuery).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockResult);
    });

    it('should select data with LIMIT', async () => {
      const mockResult = [{ id: 1, name: 'John', email: '<EMAIL>' }];
      mockExecuteQuery.mockResolvedValue(mockResult);

      const query = 'SELECT * FROM `users` LIMIT 1';
      const result = await mysqlManager.executeQuery(query);
      
      expect(mockExecuteQuery).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockResult);
    });
  });

  describe('mysql_insert_data tool', () => {
    it('should insert data successfully', async () => {
      const mockResult = { affectedRows: 1, insertId: 3 };
      mockExecuteQuery.mockResolvedValue(mockResult);

      const query = 'INSERT INTO `users` (`name`, `email`) VALUES (?, ?)';
      const params = ['Alice', '<EMAIL>'];
      const result = await mysqlManager.executeQuery(query, params);
      
      expect(mockExecuteQuery).toHaveBeenCalledWith(query, params);
      expect(result).toEqual(mockResult);
    });
  });

  describe('mysql_update_data tool', () => {
    it('should update data successfully', async () => {
      const mockResult = { affectedRows: 1, changedRows: 1 };
      mockExecuteQuery.mockResolvedValue(mockResult);

      const query = 'UPDATE `users` SET `name` = ? WHERE id = 1';
      const params = ['John Updated'];
      const result = await mysqlManager.executeQuery(query, params);
      
      expect(mockExecuteQuery).toHaveBeenCalledWith(query, params);
      expect(result).toEqual(mockResult);
    });
  });

  describe('mysql_delete_data tool', () => {
    it('should delete data successfully', async () => {
      const mockResult = { affectedRows: 1 };
      mockExecuteQuery.mockResolvedValue(mockResult);

      const query = 'DELETE FROM `users` WHERE id = 1';
      const result = await mysqlManager.executeQuery(query);
      
      expect(mockExecuteQuery).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockResult);
    });
  });

  describe('mysql_get_schema tool', () => {
    it('should return database schema', async () => {
      const mockResult = [
        {
          TABLE_NAME: 'users',
          COLUMN_NAME: 'id',
          DATA_TYPE: 'int',
          IS_NULLABLE: 'NO',
          COLUMN_DEFAULT: null,
          COLUMN_KEY: 'PRI',
          EXTRA: 'auto_increment',
          COLUMN_COMMENT: ''
        }
      ];
      mockExecuteQuery.mockResolvedValue(mockResult);

      const result = await mysqlManager.executeQuery('SELECT TABLE_NAME, COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE()');
      
      expect(result).toEqual(mockResult);
    });
  });

  describe('mysql_get_indexes tool', () => {
    it('should return index information', async () => {
      const mockResult = [
        {
          INDEX_NAME: 'PRIMARY',
          COLUMN_NAME: 'id',
          NON_UNIQUE: 0,
          SEQ_IN_INDEX: 1,
          INDEX_TYPE: 'BTREE'
        }
      ];
      mockExecuteQuery.mockResolvedValue(mockResult);

      const query = 'SELECT INDEX_NAME, COLUMN_NAME, NON_UNIQUE FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE()';
      const result = await mysqlManager.executeQuery(query);
      
      expect(result).toEqual(mockResult);
    });
  });

  describe('mysql_get_foreign_keys tool', () => {
    it('should return foreign key information', async () => {
      const mockResult = [
        {
          TABLE_NAME: 'orders',
          COLUMN_NAME: 'user_id',
          CONSTRAINT_NAME: 'fk_orders_user_id',
          REFERENCED_TABLE_NAME: 'users',
          REFERENCED_COLUMN_NAME: 'id'
        }
      ];
      mockExecuteQuery.mockResolvedValue(mockResult);

      const query = 'SELECT TABLE_NAME, COLUMN_NAME, CONSTRAINT_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE TABLE_SCHEMA = DATABASE() AND REFERENCED_TABLE_NAME IS NOT NULL';
      const result = await mysqlManager.executeQuery(query);
      
      expect(result).toEqual(mockResult);
    });
  });

  describe('mysql_create_table tool', () => {
    it('should create table successfully', async () => {
      const mockResult = { affectedRows: 0 };
      mockExecuteQuery.mockResolvedValue(mockResult);

      const query = 'CREATE TABLE `test_table` (`id` int NOT NULL AUTO_INCREMENT, `name` varchar(255), PRIMARY KEY (`id`))';
      const result = await mysqlManager.executeQuery(query);
      
      expect(mockExecuteQuery).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockResult);
    });
  });

  describe('mysql_drop_table tool', () => {
    it('should drop table successfully', async () => {
      const mockResult = { affectedRows: 0 };
      mockExecuteQuery.mockResolvedValue(mockResult);

      const query = 'DROP TABLE `test_table`';
      const result = await mysqlManager.executeQuery(query);
      
      expect(mockExecuteQuery).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockResult);
    });

    it('should drop table with IF EXISTS successfully', async () => {
      const mockResult = { affectedRows: 0 };
      mockExecuteQuery.mockResolvedValue(mockResult);

      const query = 'DROP TABLE IF EXISTS `test_table`';
      const result = await mysqlManager.executeQuery(query);
      
      expect(mockExecuteQuery).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockResult);
    });
  });
});