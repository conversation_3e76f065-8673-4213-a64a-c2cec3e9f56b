/**
 * MySQL MCP服务器 - FastMCP实现版本
 *
 * 这是一个为Model Context Protocol (MCP)设计的高性能、企业级MySQL数据库操作服务器。
 * 基于FastMCP框架构建，集成了智能缓存、性能监控、增强连接池管理和全面的安全保护机制。
 *
 * 主要特性：
 *      - FastMCP框架：提供卓越的性能和可靠性
 *      - 多级智能缓存系统：支持TTL和访问统计的LRU缓存
 *      - 增强连接池：预创建连接、健康检查和自动重连
 *      - 实时性能监控：时间序列指标收集和告警系统
 *      - 自适应速率限制：基于令牌桶算法的智能限流
 *      - 多层安全保护：SQL注入检测和输入验证
 *      - 全面错误处理：智能错误分类和诊断功能
 *      - 零配置架构：基于常量的设计，支持环境变量配置
 *
 * 架构设计原则：
 *      1. 性能优先：所有设计决策都优先考虑性能影响
 *      2. 安全第一：多重安全层，包括输入验证和SQL注入检测
 *      3. 可观测性：全面的监控和诊断能力
 *      4. 配置驱动：通过环境变量实现灵活配置
 *      5. 优雅降级：缓存失败不影响核心功能
 *
 * @fileoverview MySQL MCP 服务器的主入口点
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-08-07
 * @license MIT
 * @since 1.0.0
 */
import { FastMCP } from 'fastmcp';
import { MySQLManager } from './mysqlManager.js';
/**
 * 全局 MySQL 连接管理器实例
 * 处理所有数据库操作，包括连接池、缓存和安全验证
 */
declare const mysqlManager: MySQLManager;
/**
 * FastMCP 服务器实例配置
 * 使用常量中的服务器名称和版本进行配置
 */
declare const mcp: FastMCP<undefined>;
/**
 * 模块导出
 *
 * 导出配置的MCP服务器实例和MySQL管理器，
 * 用于外部使用、测试或与其他模块集成。
 */
export { mcp, mysqlManager };
/**
 * 服务器启动函数
 *
 * 初始化并启动MySQL MCP服务器，包含错误处理。
 * 确保在启动失败时进行适当的清理。
 *
 * @async
 * @function startServer
 * @returns {Promise<void>} 当服务器成功启动时解析的Promise
 * @throws {Error} 当服务器初始化失败时抛出
 */
export declare function startServer(): Promise<void>;
//# sourceMappingURL=index.d.ts.map