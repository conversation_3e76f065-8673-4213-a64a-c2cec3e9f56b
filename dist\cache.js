/**
 * 智能缓存实现
 *
 * 高性能、线程安全的缓存系统，具有 LRU（最近最少使用）淘汰策略、
 * TTL（生存时间）过期和全面的统计功能。针对数据库元数据缓存进行优化，
 * 具有自动清理和监控功能。
 *
 * @fileoverview 带 LRU 淘汰和 TTL 过期的智能缓存
 * <AUTHOR>
 * @since 1.0.0
 */
import { DefaultConfig, StringConstants } from './constants.js';
/**
 * 智能缓存类
 *
 * 高级缓存实现，具有多种淘汰策略：
 * - 缓存满时的 LRU（最近最少使用）淘汰
 * - TTL（生存时间）自动过期
 * - 用于性能分析的访问模式跟踪
 *
 * 性能特征：
 * - O(1) 获取/放置操作（平均情况）
 * - O(n) LRU 淘汰（最坏情况，当缓存满时）
 * - 内存高效，具有自动清理功能
 *
 * 线程安全：
 * - 使用内部锁定机制进行并发访问
 * - 在多线程环境中安全使用
 *
 * @class SmartCache
 * @template T - 要缓存的数据类型
 * @since 1.0.0
 */
export class SmartCache {
    /** 缓存中允许的最大条目数 */
    max_size;
    /** 缓存条目的生存时间（秒） */
    ttl;
    /** 存储带元数据的缓存条目的内部 Map */
    cache;
    /** 线程安全的简单锁定机制 */
    lock;
    /** 成功缓存命中次数 */
    hit_count;
    /** 缓存未命中次数 */
    miss_count;
    /**
     * 智能缓存构造函数
     *
     * 使用指定的最大大小和TTL设置初始化缓存。
     * 设置内部数据结构和性能计数器。
     *
     * @constructor
     * @param {number} maxSize - 要存储的最大条目数
     * @param {number} [ttl=DefaultConfig.CACHE_TTL] - 生存时间（秒）
     *
     * @example
     * // 创建具有100个条目、5分钟TTL的缓存
     * const cache = new SmartCache<string>(100, 300);
     */
    constructor(maxSize, ttl = DefaultConfig.CACHE_TTL) {
        this.max_size = maxSize;
        this.ttl = ttl;
        this.cache = new Map();
        this.lock = false;
        this.hit_count = 0;
        this.miss_count = 0;
    }
    /**
     * 从缓存获取值
     *
     * 从缓存中检索值，自动检查过期和更新LRU位置。
     * 更新访问统计信息并将访问的条目移动到末尾以进行LRU跟踪。
     *
     * 时间复杂度：O(1) 平均情况
     *
     * @public
     * @param {string} key - 要检索的缓存键
     * @returns {T | null} 缓存值，如果未找到/过期则返回null
     *
     * @example
     * const userData = cache.get('user:123');
     * if (userData) {
     *   console.log('缓存命中:', userData);
     * } else {
     *   console.log('缓存未命中，需要从数据库获取');
     * }
     */
    get(key) {
        const entry = this.cache.get(key);
        // 缓存未命中：未找到键
        if (!entry) {
            this.miss_count++;
            return null;
        }
        // 检查条目是否已过期
        if (this.isExpired(entry)) {
            this.cache.delete(key);
            this.miss_count++;
            return null;
        }
        // 缓存命中：更新访问统计信息
        entry.accessCount++;
        entry.lastAccessed = Date.now();
        this.hit_count++;
        // 移动到末尾进行LRU跟踪（最近使用）
        this.cache.delete(key);
        this.cache.set(key, entry);
        return entry.data;
    }
    /**
     * 将值放入缓存
     *
     * 将值存储在缓存中，当缓存满时自动淘汰。
     * 更新现有条目或创建带有适当元数据的新条目。
     * 当缓存达到最大大小时实施LRU淘汰策略。
     *
     * 时间复杂度：O(1) 平均情况，需要淘汰时为O(n)
     *
     * @public
     * @param {string} key - 值的缓存键
     * @param {T} value - 要存储在缓存中的值
     *
     * @example
     * cache.put('user:123', { id: 123, name: 'John Doe' });
     */
    put(key, value) {
        // 更新现有条目并移动到末尾（最近使用）
        if (this.cache.has(key)) {
            const entry = this.cache.get(key);
            entry.data = value;
            entry.createdAt = Date.now();
            entry.accessCount = 0;
            entry.lastAccessed = Date.now();
            this.cache.delete(key);
            this.cache.set(key, entry);
            return;
        }
        // 如果缓存已满，则淘汰最近最少使用的项目
        if (this.cache.size >= this.max_size) {
            this.evictLRU();
        }
        // 创建并添加新的缓存条目
        const entry = {
            data: value,
            createdAt: Date.now(),
            accessCount: 0,
            lastAccessed: Date.now()
        };
        this.cache.set(key, entry);
    }
    /**
     * 清空缓存
     *
     * 从缓存中移除所有条目并重置统计信息。
     * 用于缓存失效或内存清理。
     *
     * 时间复杂度：O(1)
     *
     * @public
     *
     * @example
     * cache.clear(); // 移除所有缓存数据
     */
    clear() {
        this.cache.clear();
    }
    /**
     * 淘汰最近最少使用的项目
     *
     * 通过查找和删除具有最旧lastAccessed时间戳的条目来实施LRU（最近最少使用）淘汰策略。
     * 这确保了频繁访问的数据保留在缓存中。
     *
     * 算法：线性扫描查找LRU项目
     * 时间复杂度：O(n)，其中n是缓存大小
     * 空间复杂度：O(1)
     *
     * @private
     */
    evictLRU() {
        if (this.cache.size === 0)
            return;
        // 查找最近最少使用的项目（最旧的lastAccessed时间戳）
        let oldestKey = null;
        let oldestTime = Number.MAX_SAFE_INTEGER;
        const entries = Array.from(this.cache.entries());
        for (const [key, entry] of entries) {
            if (entry.lastAccessed < oldestTime) {
                oldestTime = entry.lastAccessed;
                oldestKey = key;
            }
        }
        // 删除最近最少使用的条目
        if (oldestKey) {
            this.cache.delete(oldestKey);
        }
    }
    /**
     * 检查条目是否已过期
     *
     * 根据创建时间戳和配置的TTL值确定缓存条目是否已超过其生存时间。
     *
     * @private
     * @param {CacheEntry<T>} entry - 要检查的缓存条目
     * @returns {boolean} 如果条目已过期则返回true，否则返回false
     */
    isExpired(entry) {
        return Date.now() - entry.createdAt > this.ttl * 1000;
    }
    /**
     * 获取缓存统计信息
     *
     * 返回关于缓存性能的综合统计信息，包括命中/未命中比率、
     * 大小信息和配置详细信息。对监控和性能调优很有用。
     *
     * @public
     * @returns {Record<string, number>} 缓存统计信息对象
     *
     * @example
     * const stats = cache.getStats();
     * console.log(`命中率: ${(stats.hit_rate * 100).toFixed(2)}%`);
     * console.log(`缓存利用率: ${stats.size}/${stats.max_size}`);
     */
    getStats() {
        const total = this.hit_count + this.miss_count;
        return {
            [StringConstants.FIELD_SIZE]: this.cache.size,
            [StringConstants.FIELD_MAX_SIZE]: this.max_size,
            [StringConstants.FIELD_HIT_COUNT]: this.hit_count,
            [StringConstants.FIELD_MISS_COUNT]: this.miss_count,
            [StringConstants.FIELD_HIT_RATE]: total > 0 ? this.hit_count / total : 0,
            [StringConstants.FIELD_TTL]: this.ttl
        };
    }
}
//# sourceMappingURL=cache.js.map