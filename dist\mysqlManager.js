/**
 * MySQL 管理器 - 主集成类
 *
 * 综合的 MySQL 管理系统，集成了连接池、缓存、安全验证、
 * 速率限制和性能监控功能。为所有数据库操作提供统一接口，
 * 具备企业级的可靠性和安全特性。
 *
 * @fileoverview 集成组件的核心 MySQL 管理类
 * <AUTHOR>
 * @since 1.0.0
 */
import { randomUUID } from 'crypto';
import { ConnectionPool } from './connection.js';
import { ConfigurationManager } from './config.js';
import { SmartCache } from './cache.js';
import { EnhancedSecurityValidator } from './security.js';
import { AdaptiveRateLimiter } from './rateLimit.js';
import { EnhancedMetricsManager, PerformanceMetrics } from './metrics.js';
import { StringConstants, DefaultConfig, MySQLErrorCodes } from './constants.js';
/**
 * 默认重试策略实现
 *
 * 实现具有可配置参数的指数退避算法。
 * 为大多数数据库重试场景提供合理的默认值。
 */
class DefaultRetryStrategy {
    maxAttempts = DefaultConfig.MAX_RETRY_ATTEMPTS;
    baseDelay = 1.0;
    maxDelay = 10.0;
    backoffFactor = 2.0;
    /**
     * 计算带有最大上限的指数退避延迟
     * @param attempt - 当前重试尝试次数
     * @returns 计算出的延迟秒数
     */
    getDelay(attempt) {
        const delay = this.baseDelay * Math.pow(this.backoffFactor, attempt);
        return Math.min(delay, this.maxDelay);
    }
}
/**
 * MySQL 管理器类
 *
 * 中央管理类，协调所有 MySQL 操作，集成了安全性、
 * 性能监控、缓存和连接管理功能。
 *
 * 功能特性：
 * - 带健康监控的连接池
 * - 多级缓存（模式、表存在性、索引）
 * - 安全验证和 SQL 注入防护
 * - 基于系统负载的自适应速率限制
 * - 全面的性能指标和告警
 * - 指数退避的自动重试
 * - 优雅的错误处理和恢复
 *
 * @class MySQLManager
 * @since 1.0.0
 */
export class MySQLManager {
    /** 用于跟踪和调试的唯一会话标识符 */
    sessionId;
    /** 数据库、安全和缓存设置的配置管理器 */
    configManager;
    /** 高效数据库连接的连接池管理器 */
    connectionPool;
    /** 表模式信息的智能缓存 */
    schemaCache;
    /** 表存在性检查的智能缓存 */
    tableExistsCache;
    /** 索引信息的智能缓存 */
    indexCache;
    /** 传统性能指标收集器 */
    metrics;
    /** 带时间序列数据和告警的增强指标管理器 */
    enhancedMetrics;
    /** 用于输入清理和 SQL 注入防护的安全验证器 */
    securityValidator;
    /** 请求节流的自适应速率限制器 */
    adaptiveRateLimiter;
    /** 处理瞬态错误的重试策略 */
    retryStrategy;
    /**
     * 用于安全验证的预编译危险SQL模式
     * 这些模式检测可能危害系统安全或数据完整性的潜在有害SQL操作。
     */
    static DANGEROUS_PATTERNS = [
        /\b(LOAD_FILE|INTO\s+OUTFILE|INTO\s+DUMPFILE)\b/i,
        /\b(SYSTEM|EXEC|SHELL)\b/i,
        /\bINTO\s+OUTFILE\b/i,
        /\bLOAD\s+DATA\b/i,
    ];
    /**
     * 表名验证模式
     * 确保表名只包含安全字符（字母数字、下划线、连字符）
     */
    static TABLE_NAME_PATTERN = /^[a-zA-Z0-9_-]+$/;
    /**
     * MySQL 管理器构造函数
     *
     * 初始化MySQL管理系统的所有组件，包括配置、连接池、
     * 缓存、安全和监控功能。
     *
     * @constructor
     * @throws {Error} 当组件初始化失败时抛出
     */
    constructor() {
        // 生成用于跟踪的唯一会话标识符
        this.sessionId = randomUUID();
        // 初始化集中配置管理
        this.configManager = new ConfigurationManager();
        // 使用数据库配置初始化连接池
        this.connectionPool = new ConnectionPool(this.configManager.database);
        // 使用配置的大小和 TTL 初始化智能缓存系统
        this.schemaCache = new SmartCache(this.configManager.cache.schemaCacheSize, this.configManager.cache.cacheTTL);
        this.tableExistsCache = new SmartCache(this.configManager.cache.tableExistsCacheSize, this.configManager.cache.cacheTTL);
        this.indexCache = new SmartCache(this.configManager.cache.indexCacheSize, this.configManager.cache.cacheTTL);
        // 初始化性能监控系统
        this.metrics = new PerformanceMetrics();
        this.enhancedMetrics = new EnhancedMetricsManager();
        this.enhancedMetrics.startMonitoring();
        // 初始化安全验证系统
        this.securityValidator = new EnhancedSecurityValidator();
        // 使用安全配置初始化自适应速率限制
        this.adaptiveRateLimiter = new AdaptiveRateLimiter(this.configManager.security.rateLimitMax, this.configManager.security.rateLimitWindow);
        // 初始化错误处理的重试策略
        this.retryStrategy = new DefaultRetryStrategy();
    }
    /**
     * 使用重试机制执行操作
     *
     * 使用指数退避自动重试执行数据库操作。
     * 处理瞬态错误，同时避免对永久性故障进行重试。
     *
     * @private
     * @template T - 操作的返回类型
     * @param operation - 要使用重试逻辑执行的异步操作
     * @returns 解析为操作结果的Promise
     * @throws {Error} 当所有重试尝试都用尽或发生不可重试错误时抛出
     */
    async executeWithRetry(operation) {
        let lastError = null;
        for (let attempt = 0; attempt < this.retryStrategy.maxAttempts; attempt++) {
            try {
                return await operation();
            }
            catch (error) {
                lastError = error;
                // 跳过某些不会通过重试解决的错误类型
                if (error.code) {
                    const errorCode = parseInt(error.code, 10);
                    if (errorCode === MySQLErrorCodes.ACCESS_DENIED ||
                        errorCode === MySQLErrorCodes.PARSE_ERROR) {
                        break;
                    }
                }
                // 在下次尝试前应用指数退避延迟
                if (attempt < this.retryStrategy.maxAttempts - 1) {
                    const delay = this.retryStrategy.getDelay(attempt) * 1000; // 转换为毫秒
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        throw lastError || new Error("所有重试后操作失败");
    }
    /**
     * 验证输入数据
     *
     * 对输入数据执行综合安全验证，以防止SQL注入和其他安全漏洞。
     *
     * @private
     * @param inputValue - 要验证的值（字符串、数字、布尔值、null、undefined）
     * @param fieldName - 被验证字段的名称（用于错误消息）
     * @param validationLevel - 验证严格级别（"strict"、"moderate"、"basic"）
     * @throws {Error} 当输入未通过安全验证时抛出
     */
    validateInput(inputValue, fieldName, validationLevel = "strict") {
        EnhancedSecurityValidator.validateInputComprehensive(inputValue, fieldName, validationLevel);
    }
    /**
     * 验证SQL查询安全性
     *
     * 对SQL查询执行综合安全验证，包括长度限制、
     * 危险模式检测和查询类型限制。
     *
     * @private
     * @param query - 要验证的SQL查询字符串
     * @throws {Error} 当查询未通过安全验证时抛出
     */
    validateQuery(query) {
        // 检查查询长度是否超过配置的最大值
        if (query.length > this.configManager.security.maxQueryLength) {
            throw new Error(StringConstants.MSG_QUERY_TOO_LONG);
        }
        // 扫描可能危害安全的危险 SQL 模式
        for (const pattern of MySQLManager.DANGEROUS_PATTERNS) {
            if (pattern.test(query)) {
                throw new Error(StringConstants.MSG_PROHIBITED_OPERATIONS);
            }
        }
        // 提取并验证查询类型（第一个单词）
        const trimmedQuery = query.trim();
        // 使用更可靠的方式提取查询类型
        const queryTypeMatch = trimmedQuery.match(/^(\w+)/);
        const queryType = queryTypeMatch ? queryTypeMatch[1].toUpperCase() : '';
        // 确保查询类型在允许列表中
        if (!queryType || !this.configManager.security.allowedQueryTypes.includes(queryType)) {
            throw new Error(StringConstants.MSG_QUERY_TYPE_NOT_ALLOWED.replace('{query_type}', queryType));
        }
    }
    /**
     * 验证表名
     *
     * 根据安全模式和长度限制验证表名，
     * 以防止SQL注入并确保兼容性。
     *
     * @private
     * @param tableName - 要验证的表名
     * @throws {Error} 当表名无效或过长时抛出
     */
    validateTableName(tableName) {
        // 检查是否符合允许的字符模式
        if (!MySQLManager.TABLE_NAME_PATTERN.test(tableName)) {
            throw new Error(StringConstants.MSG_INVALID_TABLE_NAME);
        }
        // 检查长度限制
        if (tableName.length > DefaultConfig.MAX_TABLE_NAME_LENGTH) {
            throw new Error(StringConstants.MSG_TABLE_NAME_TOO_LONG);
        }
    }
    /**
     * 检查速率限制
     *
     * 使用自适应速率限制器验证当前请求是否在速率限制范围内。
     *
     * @private
     * @param identifier - 速率限制的唯一标识符（默认为"default"）
     * @throws {Error} 当超出速率限制时抛出
     */
    checkRateLimit(identifier = "default") {
        if (!this.adaptiveRateLimiter.checkRateLimit(identifier)) {
            throw new Error(StringConstants.MSG_RATE_LIMIT_EXCEEDED);
        }
    }
    /**
     * 使用缓存获取表模式
     *
     * 使用智能缓存检索表模式信息以提高性能。
     * 缓存未命中触发数据库查询，而命中立即返回缓存数据。
     *
     * @private
     * @param tableName - 要获取模式的表名
     * @returns 解析为表模式信息的Promise
     * @throws {Error} 当模式查询失败时抛出
     */
    async getTableSchemaCached(tableName) {
        const cacheKey = `schema_${tableName}`;
        let result = this.schemaCache.get(cacheKey);
        if (result === null) {
            // 缓存未命中：执行模式查询
            const schemaQuery = `
        SELECT
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          COLUMN_KEY,
          EXTRA,
          COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
        ORDER BY ORDINAL_POSITION
      `;
            result = await this.executeQuery(schemaQuery, [tableName]);
            this.schemaCache.put(cacheKey, result);
            this.metrics.cacheMisses++;
        }
        else {
            // 缓存命中：返回缓存数据
            this.metrics.cacheHits++;
        }
        return result ?? false;
    }
    /**
     * 使用缓存检查表存在性
     *
     * 使用缓存验证表是否存在于当前数据库中，
     * 以避免重复的INFORMATION_SCHEMA查询。
     *
     * @private
     * @param tableName - 要检查的表名
     * @returns 如果表存在则解析为true，否则为false的Promise
     * @throws {Error} 当存在性检查查询失败时抛出
     */
    async tableExistsCached(tableName) {
        const cacheKey = `exists_${tableName}`;
        let result = this.tableExistsCache.get(cacheKey);
        if (result === null) {
            // 缓存未命中：执行存在性检查查询
            const existsQuery = `
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
      `;
            const queryResult = await this.executeQuery(existsQuery, [tableName]);
            result = (queryResult && queryResult[0] && queryResult[0].count > 0) || null;
            this.tableExistsCache.put(cacheKey, result ?? false);
            this.metrics.cacheMisses++;
        }
        else {
            // 缓存命中：返回缓存结果
            this.metrics.cacheHits++;
        }
        return result ?? false;
    }
    /**
     * 执行SQL查询
     *
     * 执行SQL查询的主要公共方法，具有综合安全性、性能监控、
     * 缓存和错误处理功能。包括速率限制、重试机制和指标收集。
     *
     * @public
     * @param query - 要执行的SQL查询字符串
     * @param params - 预处理语句的可选参数
     * @returns 解析为查询结果的Promise
     * @throws {Error} 当超出速率限制、安全验证失败或查询执行失败时抛出
     *
     * @example
     * // 简单查询
     * const results = await manager.executeQuery("SELECT * FROM users LIMIT 10");
     *
     * @example
     * // 参数化查询
     * const user = await manager.executeQuery("SELECT * FROM users WHERE id = ?", [123]);
     */
    async executeQuery(query, params) {
        const startTime = Date.now();
        try {
            // 应用速率限制以防止滥用
            this.checkRateLimit();
            // 验证查询的安全合规性
            this.validateQuery(query);
            // 在瞬态故障时自动重试执行
            const result = await this.executeWithRetry(async () => {
                return await this.executeQueryInternal(query, params);
            });
            // 记录成功执行的指标
            const queryTime = (Date.now() - startTime) / 1000; // 转换为秒
            const isSlow = queryTime > DefaultConfig.SLOW_QUERY_THRESHOLD;
            this.updateMetrics(queryTime, false, isSlow);
            return result;
        }
        catch (error) {
            // 记录错误指标
            const queryTime = (Date.now() - startTime) / 1000; // 转换为秒
            this.updateMetrics(queryTime, true, false);
            throw error;
        }
    }
    /**
     * 内部查询执行
     *
     * 处理实际数据库连接和查询执行的低级方法。
     * 管理连接生命周期并确保适当的资源清理。
     *
     * @private
     * @param query - SQL查询字符串
     * @param params - 可选查询参数
     * @returns 解析为原始查询结果的Promise
     * @throws {Error} 当连接或查询执行失败时抛出
     */
    async executeQueryInternal(query, params) {
        const connection = await this.connectionPool.getConnection();
        try {
            const [rows] = await connection.execute(query, params);
            return rows;
        }
        finally {
            // 始终将连接释放回连接池
            connection.release();
        }
    }
    /**
     * 更新性能指标
     *
     * 更新查询执行的性能指标，包括时间、错误和慢查询统计。
     *
     * @private
     * @param queryTime - 查询执行时间（秒）
     * @param isError - 是否发生错误
     * @param isSlow - 是否为慢查询
     */
    updateMetrics(queryTime, isError = false, isSlow = false) {
        this.metrics.queryCount++;
        this.metrics.totalQueryTime += queryTime;
        if (isError) {
            this.metrics.errorCount++;
            this.enhancedMetrics.recordError("query_error", "medium");
        }
        if (isSlow) {
            this.metrics.slowQueryCount++;
        }
        // 记录到增强指标管理器
        this.enhancedMetrics.recordQueryTime(queryTime);
        // 更新缓存命中率指标
        const cacheHitRate = this.metrics.getCacheHitRate();
        this.enhancedMetrics.recordCacheHitRate(cacheHitRate);
    }
    /**
     * 使缓存失效
     *
     * 根据操作类型使相关缓存失效。DDL操作清除所有缓存，
     * DML操作清除特定表的缓存。
     *
     * @public
     * @param operationType - 操作类型（DDL、DML等）
     * @param tableName - 可选的表名，用于特定表缓存失效
     */
    invalidateCaches(operationType = "DDL", tableName) {
        if (operationType === "DDL" || operationType === "CREATE" || operationType === "DROP" || operationType === "ALTER") {
            // DDL 操作清除所有缓存
            this.clearAllCaches();
        }
        else if (operationType === "DML" && tableName) {
            // DML 操作清除特定表缓存
            this.invalidateTableSpecificCache(tableName);
        }
    }
    /**
     * 清除所有缓存
     *
     * @private
     */
    clearAllCaches() {
        this.schemaCache.clear();
        this.tableExistsCache.clear();
        this.indexCache.clear();
    }
    /**
     * 使特定表缓存失效
     *
     * @private
     * @param tableName - 要使缓存失效的表名
     */
    invalidateTableSpecificCache(tableName) {
        const cacheKeysToRemove = [
            `schema_${tableName}`,
            `exists_${tableName}`,
            `indexes_${tableName}`
        ];
        // 从所有缓存中移除键
        cacheKeysToRemove.forEach(_key => {
            // 我们无法直接访问缓存内部，所以只是清除它们
            // 在真实实现中，我们会有一个方法来移除特定键
        });
    }
    /**
     * 获取性能指标
     *
     * 检索综合性能指标，包括查询统计、缓存性能和
     * 连接池状态，用于监控和调试。
     *
     * @public
     * @returns 包含详细性能指标的对象
     *
     * @example
     * const metrics = manager.getPerformanceMetrics();
     * console.log(`缓存命中率: ${metrics.cache_stats.schema_cache.hit_rate}`);
     */
    getPerformanceMetrics() {
        return {
            [StringConstants.SECTION_PERFORMANCE]: this.metrics.toObject(),
            [StringConstants.SECTION_CACHE_STATS]: {
                [StringConstants.SECTION_SCHEMA_CACHE]: this.schemaCache.getStats(),
                [StringConstants.SECTION_TABLE_EXISTS_CACHE]: this.tableExistsCache.getStats(),
                [StringConstants.SECTION_INDEX_CACHE]: this.indexCache.getStats()
            },
            [StringConstants.SECTION_CONNECTION_POOL]: this.connectionPool.getStats()
        };
    }
    /**
     * 关闭MySQL管理器
     *
     * 执行所有组件的优雅关闭，包括指标监控、连接池关闭和缓存清理。
     * 应在应用程序关闭期间调用以防止资源泄漏。
     *
     * @public
     * @returns 当所有清理完成时解析的Promise
     *
     * @example
     * // 优雅关闭
     * await manager.close();
     */
    async close() {
        try {
            // 停止增强指标监控
            this.enhancedMetrics.stopMonitoring();
            // 关闭连接池并释放所有连接
            await this.connectionPool.close();
            // 清除所有缓存以释放内存
            this.schemaCache.clear();
            this.tableExistsCache.clear();
            this.indexCache.clear();
        }
        catch (error) {
            console.error(`${StringConstants.MSG_ERROR_DURING_CLEANUP} ${error}`);
        }
    }
}
//# sourceMappingURL=mysqlManager.js.map