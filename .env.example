# Enhanced MySQL MCP Server - Environment Configuration
# 环境配置文件，请根据实际情况修改配置

# ===========================================
# MySQL 数据库连接配置 (必填)
# ===========================================
DB_HOST=localhost
DB_PORT=3306
DB_USER=mysql_user
DB_PASSWORD=your_secure_password
DB_NAME=test_database

# SSL 连接配置 (可选)
DB_SSL=false

# 字符集和时区配置 (可选)
DB_CHARSET=utf8mb4
DB_TIMEZONE=+00:00

# ===========================================
# 连接池配置 (可选)
# ===========================================
# 最大连接数
DB_CONNECTION_LIMIT=20

# 连接超时时间 (毫秒)
DB_CONNECT_TIMEOUT=60000

# 空闲超时时间 (毫秒)
DB_IDLE_TIMEOUT=300000

# 查询超时时间 (毫秒)
DB_QUERY_TIMEOUT=30000

# ===========================================
# 缓存系统配置 (可选)
# ===========================================
# 表结构缓存大小
CACHE_SCHEMA_SIZE=128

# 查询结果缓存大小
CACHE_QUERY_SIZE=256

# 表存在性检查缓存大小
CACHE_TABLE_EXISTS_SIZE=64

# 缓存TTL时间 (秒)
CACHE_TTL=300

# ===========================================
# 安全配置 (可选)
# ===========================================
# 最大查询长度
SECURITY_MAX_QUERY_LENGTH=10000

# 最大输入长度
SECURITY_MAX_INPUT_LENGTH=1000

# 最大表名长度
SECURITY_MAX_TABLE_NAME_LENGTH=64

# 允许的查询类型 (逗号分隔)
SECURITY_ALLOWED_QUERY_TYPES=SELECT,INSERT,UPDATE,DELETE,SHOW,DESCRIBE,EXPLAIN

# 是否阻止危险查询
SECURITY_BLOCK_DANGEROUS_QUERIES=true

# 是否启用审计日志
SECURITY_ENABLE_AUDIT_LOG=true

# ===========================================
# 频率限制配置 (可选)
# ===========================================
# 每分钟最大请求数
RATE_LIMIT_MAX=100

# 时间窗口 (毫秒)
RATE_LIMIT_WINDOW=60000

# 是否启用频率限制
RATE_LIMIT_ENABLED=true

# ===========================================
# 监控配置 (可选)
# ===========================================
# 是否启用性能监控
MONITORING_ENABLED=true

# 性能快照间隔 (毫秒)
MONITORING_SNAPSHOT_INTERVAL=30000

# 指标历史记录大小
MONITORING_HISTORY_SIZE=1000

# 慢查询阈值 (毫秒)
MONITORING_SLOW_QUERY_THRESHOLD=1000

# ===========================================
# 诊断配置 (可选)  
# ===========================================
# 是否启用诊断工具
DIAGNOSTIC_ENABLED=true

# 健康检查间隔 (毫秒)
DIAGNOSTIC_HEALTH_CHECK_INTERVAL=30000

# 自动生成报告间隔 (毫秒)
DIAGNOSTIC_REPORT_INTERVAL=300000

# ===========================================
# 服务器配置 (可选)
# ===========================================
# 服务器名称
SERVER_NAME=Enhanced-MySQL-MCP-Server

# 服务器版本
SERVER_VERSION=3.0.0

# 运行环境 (development, production, test)
NODE_ENV=development

# 日志级别 (debug, info, warn, error)
LOG_LEVEL=info

# 是否启用调试模式
DEBUG=false

# ===========================================
# MCP 协议配置 (可选)
# ===========================================
# MCP 服务器传输协议 (stdio, websocket, http)
MCP_TRANSPORT=stdio

# WebSocket 端口 (仅在 websocket 模式下)
MCP_WEBSOCKET_PORT=3001

# HTTP 端口 (仅在 http 模式下)
MCP_HTTP_PORT=3002

# ===========================================
# 高级配置 (可选)
# ===========================================
# 内存警告阈值 (MB)
MEMORY_WARNING_THRESHOLD=512

# 内存错误阈值 (MB) 
MEMORY_ERROR_THRESHOLD=1024

# 缓存清理间隔 (毫秒)
CACHE_CLEANUP_INTERVAL=300000

# 连接池健康检查间隔 (毫秒)
POOL_HEALTH_CHECK_INTERVAL=30000

# 最大健康检查失败次数
POOL_HEALTH_CHECK_MAX_FAILURES=5

# 队列限制
QUEUE_LIMIT=0

# 最大空闲连接数
MAX_IDLE_CONNECTIONS=10

# Keep-Alive 初始延迟 (毫秒)
KEEP_ALIVE_INITIAL_DELAY=0

# ===========================================
# 兼容性配置 (向后兼容原有配置)
# ===========================================
MYSQL_HOST=${DB_HOST}
MYSQL_PORT=${DB_PORT}
MYSQL_USER=${DB_USER}
MYSQL_PASSWORD=${DB_PASSWORD}
MYSQL_DATABASE=${DB_NAME}
MYSQL_CONNECTION_LIMIT=${DB_CONNECTION_LIMIT}
MYSQL_CONNECT_TIMEOUT=${DB_CONNECT_TIMEOUT}
MYSQL_IDLE_TIMEOUT=${DB_IDLE_TIMEOUT}
MYSQL_SSL=${DB_SSL}
MYSQL_CHARSET=${DB_CHARSET}
MYSQL_TIMEZONE=${DB_TIMEZONE}

# 其他兼容性配置
MAX_QUERY_LENGTH=${SECURITY_MAX_QUERY_LENGTH}
ALLOWED_QUERY_TYPES=${SECURITY_ALLOWED_QUERY_TYPES}
SCHEMA_CACHE_SIZE=${CACHE_SCHEMA_SIZE}
TABLE_EXISTS_CACHE_SIZE=${CACHE_TABLE_EXISTS_SIZE}
INDEX_CACHE_SIZE=64
ENABLE_AUDIT_LOG=${SECURITY_ENABLE_AUDIT_LOG}
BLOCK_DANGEROUS_QUERIES=${SECURITY_BLOCK_DANGEROUS_QUERIES}
SLOW_QUERY_THRESHOLD=1.0
QUERY_TIMEOUT=${DB_QUERY_TIMEOUT}
MAX_RESULT_ROWS=1000
BATCH_SIZE=1000

# ===========================================
# 使用说明
# ===========================================
# 1. 复制此文件为 .env: cp .env.example .env
# 2. 根据实际环境修改数据库连接参数
# 3. 调整性能和安全配置
# 4. 启动服务器：npm start 或 npm run dev

# 生产环境建议配置:
# - DB_CONNECTION_LIMIT=50
# - CACHE_SCHEMA_SIZE=256
# - CACHE_QUERY_SIZE=512
# - RATE_LIMIT_MAX=1000
# - NODE_ENV=production
# - LOG_LEVEL=warn

# 开发环境建议配置:
# - DB_CONNECTION_LIMIT=10
# - DEBUG=true
# - NODE_ENV=development
# - LOG_LEVEL=debug