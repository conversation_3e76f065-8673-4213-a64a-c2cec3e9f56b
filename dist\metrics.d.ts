/**
 * 性能指标实现
 *
 * 综合性能监控系统，具有时间序列数据收集、统计分析和告警功能。
 * 提供系统性能、查询执行、缓存效率和资源利用率的实时洞察。
 *
 * @fileoverview 高级性能指标和监控系统
 * <AUTHOR>
 * @since 1.0.0
 */
/**
 * 时间序列指标类
 *
 * 管理具有自动保留、统计分析和高效存储的时间序列数据。
 * 提供百分位数计算、趋势分析和内存高效的数据管理。
 *
 * 功能特性：
 * - 基于时间和数量限制的自动数据保留
 * - 统计分析（最小值、最大值、平均值、百分位数）
 * - 内存高效的循环缓冲区实现
 * - 基于标签的维度分析
 * - 实时数据聚合
 *
 * @class TimeSeriesMetrics
 * @since 1.0.0
 */
export declare class TimeSeriesMetrics {
    /** 要保留的最大数据点数 */
    private maxPoints;
    /** 数据保留期（秒） */
    private retentionSeconds;
    /** 指标数据点数组 */
    private points;
    /**
     * 时间序列指标构造函数
     *
     * 使用指定的保留限制初始化时间序列。
     * 数据会根据时间和数量自动清理。
     *
     * @constructor
     * @param {number} [maxPoints=1000] - 要保留的最大数据点数
     * @param {number} [retentionSeconds=3600] - 数据保留期（秒）
     *
     * @example
     * // 创建具有1小时保留期、最多500个数据点的指标
     * const metrics = new TimeSeriesMetrics(500, 3600);
     */
    constructor(maxPoints?: number, retentionSeconds?: number);
    /**
     * 添加指标点
     *
     * 向时间序列添加新的数据点，自动清理过期数据。
     * 维护基于时间和计数的限制。
     *
     * @public
     * @param {number} value - 要记录的数值
     * @param {Record<string, string>} [labels] - 用于维度分析的可选标签
     *
     * @example
     * // 记录查询执行时间
     * metrics.addPoint(1.25, { query_type: 'SELECT', table: 'users' });
     *
     * @example
     * // 记录简单指标
     * metrics.addPoint(42);
     */
    addPoint(value: number, labels?: Record<string, string>): void;
    /**
     * 获取最近时间段的统计信息
     *
     * 计算指定时间窗口内数据点的综合统计信息。
     * 包括基本统计和百分位数计算。
     *
     * @public
     * @param {number} [sinceSeconds=300] - 时间窗口（秒）（默认：5分钟）
     * @returns {Record<string, number>} 最近数据的统计摘要
     *
     * @example
     * // 获取最近10分钟的统计信息
     * const stats = metrics.getStats(600);
     * console.log(`平均值: ${stats.avg}, P95: ${stats.p95}`);
     */
    getStats(sinceSeconds?: number): Record<string, number>;
    /**
     * 计算百分位数
     *
     * 使用最近排名方法从数据集计算指定的百分位数值，
     * 以获得准确的百分位数估计。
     *
     * @private
     * @param {number[]} values - 数值数组
     * @param {number} p - 要计算的百分位数（0.0 到 1.0）
     * @returns {number} 百分位数值
     */
    private percentile;
}
/**
 * 增强指标管理器
 *
 * 具有时间序列数据收集、告警和综合性能监控的高级指标管理系统。
 * 管理多种指标类型，具有自动系统监控和告警生成功能。
 *
 * 功能特性：
 * - 多维时间序列指标
 * - 可配置的告警系统，支持回调
 * - 自动系统资源监控
 * - 性能趋势分析
 * - 实时指标聚合
 * - 内存高效的数据保留
 *
 * @class EnhancedMetricsManager
 * @since 1.0.0
 */
export declare class EnhancedMetricsManager {
    /** 查询执行时间指标 */
    queryTimes: TimeSeriesMetrics;
    /** 错误发生指标 */
    errorCounts: TimeSeriesMetrics;
    /** 缓存命中率指标 */
    cacheHitRates: TimeSeriesMetrics;
    /** 系统资源利用率指标 */
    systemMetrics: TimeSeriesMetrics;
    /** 告警回调函数数组 */
    private alertCallbacks;
    /** 告警规则配置 */
    private alertRules;
    /** 优雅终止的关闭事件标志 */
    private shutdownEvent;
    /** 系统监控间隔计时器 */
    private metricsInterval;
    /**
     * 增强指标管理器构造函数
     *
     * 初始化所有指标收集器并设置默认告警规则。
     * 为不同指标类型创建时间序列实例。
     *
     * @constructor
     */
    constructor();
    /**
     * 开始监控
     *
     * 开始定期自动收集系统指标。
     * 收集CPU、内存和其他系统指标用于性能分析。
     *
     * @public
     */
    startMonitoring(): void;
    /**
     * 停止监控
     *
     * 停止自动系统指标收集并清理资源。
     * 应在应用程序关闭期间调用。
     *
     * @public
     */
    stopMonitoring(): void;
    /**
     * 记录查询时间
     *
     * 记录查询执行时间，可选择查询类型标签。
     * 自动为慢查询触发告警。
     *
     * @public
     * @param {number} duration - 查询执行时间（秒）
     * @param {string} [queryType] - 用于分类的可选查询类型
     *
     * @example
     * manager.recordQueryTime(1.25, 'SELECT');
     */
    recordQueryTime(duration: number, queryType?: string): void;
    /**
     * 记录错误
     *
     * 记录错误发生情况，包含类型和严重性分类。
     * 自动为高严重性错误触发告警。
     *
     * @public
     * @param {string} errorType - 错误的类型/类别
     * @param {string} [severity="medium"] - 错误严重性级别
     *
     * @example
     * manager.recordError('connection_timeout', 'high');
     */
    recordError(errorType: string, severity?: string): void;
    /**
     * 记录缓存命中率
     *
     * 记录缓存性能指标，可选择缓存类型标签。
     * 自动为缓存性能差触发告警。
     *
     * @public
     * @param {number} hitRate - 缓存命中率（0.0 到 1.0）
     * @param {string} [cacheType] - 用于分类的可选缓存类型
     *
     * @example
     * manager.recordCacheHitRate(0.85, 'schema_cache');
     */
    recordCacheHitRate(hitRate: number, cacheType?: string): void;
    /**
     * 收集系统指标
     *
     * 收集系统资源使用情况指标，如CPU和内存使用率。
     * 在实际实现中会收集真实的系统指标。
     *
     * @private
     */
    private collectSystemMetrics;
    /**
     * 设置默认告警规则
     *
     * 配置各种指标的默认告警阈值和窗口。
     *
     * @private
     * @returns {Record<string, Record<string, unknown>>} 告警规则配置
     */
    private setupDefaultAlertRules;
    /**
     * 添加告警回调
     *
     * 注册告警触发时要调用的回调函数。
     *
     * @public
     * @param {Function} callback - 告警回调函数
     */
    addAlertCallback(callback: (alertType: string, context: Record<string, unknown>) => void): void;
    /**
     * 触发告警
     *
     * 执行所有注册的告警回调函数。
     *
     * @private
     * @param {string} alertType - 告警类型
     * @param {Record<string, unknown>} context - 告警上下文信息
     */
    private triggerAlert;
    /**
     * 获取综合指标
     *
     * 返回所有指标类型的综合统计信息。
     *
     * @public
     * @returns {Record<string, unknown>} 综合指标数据
     */
    getComprehensiveMetrics(): Record<string, unknown>;
}
/**
 * 性能指标类（遗留兼容性）
 *
 * 简单的性能指标收集器，用于向后兼容。
 * 提供基本的查询、缓存和连接池统计信息。
 *
 * @class PerformanceMetrics
 * @since 1.0.0
 */
export declare class PerformanceMetrics {
    queryCount: number;
    totalQueryTime: number;
    slowQueryCount: number;
    errorCount: number;
    cacheHits: number;
    cacheMisses: number;
    connectionPoolHits: number;
    connectionPoolWaits: number;
    /**
     * 获取平均查询时间
     *
     * @public
     * @returns {number} 平均查询执行时间
     */
    getAvgQueryTime(): number;
    /**
     * 获取缓存命中率
     *
     * @public
     * @returns {number} 缓存命中率（0.0 到 1.0）
     */
    getCacheHitRate(): number;
    /**
     * 获取错误率
     *
     * @public
     * @returns {number} 错误率（0.0 到 1.0）
     */
    getErrorRate(): number;
    /**
     * 转换为对象
     *
     * @public
     * @returns {Record<string, unknown>} 性能指标对象
     */
    toObject(): Record<string, unknown>;
}
//# sourceMappingURL=metrics.d.ts.map