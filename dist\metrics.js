/**
 * 性能指标实现
 *
 * 综合性能监控系统，具有时间序列数据收集、统计分析和告警功能。
 * 提供系统性能、查询执行、缓存效率和资源利用率的实时洞察。
 *
 * @fileoverview 高级性能指标和监控系统
 * <AUTHOR>
 * @since 1.0.0
 */
import { StringConstants } from './constants.js';
/**
 * 时间序列指标类
 *
 * 管理具有自动保留、统计分析和高效存储的时间序列数据。
 * 提供百分位数计算、趋势分析和内存高效的数据管理。
 *
 * 功能特性：
 * - 基于时间和数量限制的自动数据保留
 * - 统计分析（最小值、最大值、平均值、百分位数）
 * - 内存高效的循环缓冲区实现
 * - 基于标签的维度分析
 * - 实时数据聚合
 *
 * @class TimeSeriesMetrics
 * @since 1.0.0
 */
export class TimeSeriesMetrics {
    /** 要保留的最大数据点数 */
    maxPoints;
    /** 数据保留期（秒） */
    retentionSeconds;
    /** 指标数据点数组 */
    points;
    /**
     * 时间序列指标构造函数
     *
     * 使用指定的保留限制初始化时间序列。
     * 数据会根据时间和数量自动清理。
     *
     * @constructor
     * @param {number} [maxPoints=1000] - 要保留的最大数据点数
     * @param {number} [retentionSeconds=3600] - 数据保留期（秒）
     *
     * @example
     * // 创建具有1小时保留期、最多500个数据点的指标
     * const metrics = new TimeSeriesMetrics(500, 3600);
     */
    constructor(maxPoints = 1000, retentionSeconds = 3600) {
        this.maxPoints = maxPoints;
        this.retentionSeconds = retentionSeconds;
        this.points = [];
    }
    /**
     * 添加指标点
     *
     * 向时间序列添加新的数据点，自动清理过期数据。
     * 维护基于时间和计数的限制。
     *
     * @public
     * @param {number} value - 要记录的数值
     * @param {Record<string, string>} [labels] - 用于维度分析的可选标签
     *
     * @example
     * // 记录查询执行时间
     * metrics.addPoint(1.25, { query_type: 'SELECT', table: 'users' });
     *
     * @example
     * // 记录简单指标
     * metrics.addPoint(42);
     */
    addPoint(value, labels) {
        const now = Date.now() / 1000; // 转换为秒
        // 根据保留策略清理过期数据点
        this.points = this.points.filter(point => (now - point.timestamp) <= this.retentionSeconds);
        // 添加新数据点
        this.points.push({
            timestamp: now,
            value: value,
            labels: labels
        });
        // 维持最大数据点限制（循环缓冲区行为）
        if (this.points.length > this.maxPoints) {
            this.points = this.points.slice(-this.maxPoints);
        }
    }
    /**
     * 获取最近时间段的统计信息
     *
     * 计算指定时间窗口内数据点的综合统计信息。
     * 包括基本统计和百分位数计算。
     *
     * @public
     * @param {number} [sinceSeconds=300] - 时间窗口（秒）（默认：5分钟）
     * @returns {Record<string, number>} 最近数据的统计摘要
     *
     * @example
     * // 获取最近10分钟的统计信息
     * const stats = metrics.getStats(600);
     * console.log(`平均值: ${stats.avg}, P95: ${stats.p95}`);
     */
    getStats(sinceSeconds = 300) {
        const cutoff = (Date.now() / 1000) - sinceSeconds;
        const recentPoints = this.points.filter(p => p.timestamp >= cutoff).map(p => p.value);
        // 处理空数据集
        if (recentPoints.length === 0) {
            return {
                count: 0,
                avg: 0,
                min: 0,
                max: 0,
                sum: 0
            };
        }
        // 计算基本统计信息
        const sum = recentPoints.reduce((a, b) => a + b, 0);
        const avg = sum / recentPoints.length;
        const min = Math.min(...recentPoints);
        const max = Math.max(...recentPoints);
        const p95 = this.percentile(recentPoints, 0.95);
        const p99 = this.percentile(recentPoints, 0.99);
        return {
            count: recentPoints.length,
            avg: avg,
            min: min,
            max: max,
            sum: sum,
            p95: p95,
            p99: p99
        };
    }
    /**
     * 计算百分位数
     *
     * 使用最近排名方法从数据集计算指定的百分位数值，
     * 以获得准确的百分位数估计。
     *
     * @private
     * @param {number[]} values - 数值数组
     * @param {number} p - 要计算的百分位数（0.0 到 1.0）
     * @returns {number} 百分位数值
     */
    percentile(values, p) {
        if (values.length === 0)
            return 0;
        const sorted = [...values].sort((a, b) => a - b);
        const index = Math.floor(sorted.length * p);
        return sorted[Math.min(index, sorted.length - 1)];
    }
}
/**
 * 增强指标管理器
 *
 * 具有时间序列数据收集、告警和综合性能监控的高级指标管理系统。
 * 管理多种指标类型，具有自动系统监控和告警生成功能。
 *
 * 功能特性：
 * - 多维时间序列指标
 * - 可配置的告警系统，支持回调
 * - 自动系统资源监控
 * - 性能趋势分析
 * - 实时指标聚合
 * - 内存高效的数据保留
 *
 * @class EnhancedMetricsManager
 * @since 1.0.0
 */
export class EnhancedMetricsManager {
    /** 查询执行时间指标 */
    queryTimes;
    /** 错误发生指标 */
    errorCounts;
    /** 缓存命中率指标 */
    cacheHitRates;
    /** 系统资源利用率指标 */
    systemMetrics;
    /** 告警回调函数数组 */
    alertCallbacks = [];
    /** 告警规则配置 */
    alertRules;
    /** 优雅终止的关闭事件标志 */
    shutdownEvent = false;
    /** 系统监控间隔计时器 */
    metricsInterval = null;
    /**
     * 增强指标管理器构造函数
     *
     * 初始化所有指标收集器并设置默认告警规则。
     * 为不同指标类型创建时间序列实例。
     *
     * @constructor
     */
    constructor() {
        this.queryTimes = new TimeSeriesMetrics();
        this.errorCounts = new TimeSeriesMetrics();
        this.cacheHitRates = new TimeSeriesMetrics();
        this.systemMetrics = new TimeSeriesMetrics();
        this.alertRules = this.setupDefaultAlertRules();
    }
    /**
     * 开始监控
     *
     * 开始定期自动收集系统指标。
     * 收集CPU、内存和其他系统指标用于性能分析。
     *
     * @public
     */
    startMonitoring() {
        if (!this.metricsInterval) {
            this.metricsInterval = setInterval(() => {
                this.collectSystemMetrics();
            }, 30000); // 每30秒收集一次
        }
    }
    /**
     * 停止监控
     *
     * 停止自动系统指标收集并清理资源。
     * 应在应用程序关闭期间调用。
     *
     * @public
     */
    stopMonitoring() {
        this.shutdownEvent = true;
        if (this.metricsInterval) {
            clearInterval(this.metricsInterval);
            this.metricsInterval = null;
        }
    }
    /**
     * 记录查询时间
     *
     * 记录查询执行时间，可选择查询类型标签。
     * 自动为慢查询触发告警。
     *
     * @public
     * @param {number} duration - 查询执行时间（秒）
     * @param {string} [queryType] - 用于分类的可选查询类型
     *
     * @example
     * manager.recordQueryTime(1.25, 'SELECT');
     */
    recordQueryTime(duration, queryType) {
        const labels = queryType ? { query_type: queryType } : undefined;
        this.queryTimes.addPoint(duration, labels);
        // 自动慢查询检测和告警
        if (duration > 2.0) { // 慢查询阈值：2秒
            this.triggerAlert("Slow Query", { duration: duration, query_type: queryType });
        }
    }
    /**
     * 记录错误
     *
     * 记录错误发生情况，包含类型和严重性分类。
     * 自动为高严重性错误触发告警。
     *
     * @public
     * @param {string} errorType - 错误的类型/类别
     * @param {string} [severity="medium"] - 错误严重性级别
     *
     * @example
     * manager.recordError('connection_timeout', 'high');
     */
    recordError(errorType, severity = "medium") {
        this.errorCounts.addPoint(1, { error_type: errorType, severity: severity });
        // 自动高严重性错误告警
        if (severity === "high") {
            this.triggerAlert("High Severity Error", { error_type: errorType });
        }
    }
    /**
     * 记录缓存命中率
     *
     * 记录缓存性能指标，可选择缓存类型标签。
     * 自动为缓存性能差触发告警。
     *
     * @public
     * @param {number} hitRate - 缓存命中率（0.0 到 1.0）
     * @param {string} [cacheType] - 用于分类的可选缓存类型
     *
     * @example
     * manager.recordCacheHitRate(0.85, 'schema_cache');
     */
    recordCacheHitRate(hitRate, cacheType) {
        const labels = cacheType ? { cache_type: cacheType } : undefined;
        this.cacheHitRates.addPoint(hitRate, labels);
        // 自动低缓存命中率告警
        if (hitRate < 0.6) { // 命中率阈值：60%
            this.triggerAlert("Low Cache Hit Rate", { hit_rate: hitRate, cache_type: cacheType });
        }
    }
    /**
     * 收集系统指标
     *
     * 收集系统资源使用情况指标，如CPU和内存使用率。
     * 在实际实现中会收集真实的系统指标。
     *
     * @private
     */
    collectSystemMetrics() {
        // 在实际实现中，我们会收集真实的系统指标
        // 现在我们只是模拟这个过程
        try {
            // 这里是我们收集CPU和内存使用情况的地方
            // 由于在此环境中无法访问系统指标，
            // 我们将跳过实际收集
        }
        catch {
            // 不要让指标收集影响系统运行
        }
    }
    /**
     * 设置默认告警规则
     *
     * 配置各种指标的默认告警阈值和窗口。
     *
     * @private
     * @returns {Record<string, Record<string, unknown>>} 告警规则配置
     */
    setupDefaultAlertRules() {
        return {
            "Slow Query": { threshold: 2.0, window: 300, count: 5 },
            "High Error Rate": { threshold: 0.05, window: 300 },
            "Low Cache Hit Rate": { threshold: 0.6, window: 600 }
        };
    }
    /**
     * 添加告警回调
     *
     * 注册告警触发时要调用的回调函数。
     *
     * @public
     * @param {Function} callback - 告警回调函数
     */
    addAlertCallback(callback) {
        this.alertCallbacks.push(callback);
    }
    /**
     * 触发告警
     *
     * 执行所有注册的告警回调函数。
     *
     * @private
     * @param {string} alertType - 告警类型
     * @param {Record<string, unknown>} context - 告警上下文信息
     */
    triggerAlert(alertType, context) {
        for (const callback of this.alertCallbacks) {
            try {
                callback(alertType, context);
            }
            catch {
                // 不要让告警失败影响系统
            }
        }
    }
    /**
     * 获取综合指标
     *
     * 返回所有指标类型的综合统计信息。
     *
     * @public
     * @returns {Record<string, unknown>} 综合指标数据
     */
    getComprehensiveMetrics() {
        return {
            query_performance: this.queryTimes.getStats(),
            error_statistics: this.errorCounts.getStats(),
            cache_performance: this.cacheHitRates.getStats(),
            system_metrics: this.systemMetrics.getStats(),
            alert_rules: this.alertRules
        };
    }
}
/**
 * 性能指标类（遗留兼容性）
 *
 * 简单的性能指标收集器，用于向后兼容。
 * 提供基本的查询、缓存和连接池统计信息。
 *
 * @class PerformanceMetrics
 * @since 1.0.0
 */
export class PerformanceMetrics {
    queryCount = 0;
    totalQueryTime = 0.0;
    slowQueryCount = 0;
    errorCount = 0;
    cacheHits = 0;
    cacheMisses = 0;
    connectionPoolHits = 0;
    connectionPoolWaits = 0;
    /**
     * 获取平均查询时间
     *
     * @public
     * @returns {number} 平均查询执行时间
     */
    getAvgQueryTime() {
        return this.totalQueryTime / Math.max(this.queryCount, 1);
    }
    /**
     * 获取缓存命中率
     *
     * @public
     * @returns {number} 缓存命中率（0.0 到 1.0）
     */
    getCacheHitRate() {
        const total = this.cacheHits + this.cacheMisses;
        return this.cacheHits / Math.max(total, 1);
    }
    /**
     * 获取错误率
     *
     * @public
     * @returns {number} 错误率（0.0 到 1.0）
     */
    getErrorRate() {
        return this.errorCount / Math.max(this.queryCount, 1);
    }
    /**
     * 转换为对象
     *
     * @public
     * @returns {Record<string, unknown>} 性能指标对象
     */
    toObject() {
        return {
            [StringConstants.FIELD_QUERY_COUNT]: this.queryCount,
            [StringConstants.FIELD_AVG_QUERY_TIME]: this.getAvgQueryTime(),
            [StringConstants.FIELD_SLOW_QUERY_COUNT]: this.slowQueryCount,
            [StringConstants.FIELD_ERROR_COUNT]: this.errorCount,
            [StringConstants.FIELD_ERROR_RATE]: this.getErrorRate(),
            [StringConstants.FIELD_CACHE_HIT_RATE]: this.getCacheHitRate(),
            [StringConstants.FIELD_CONNECTION_POOL_HITS]: this.connectionPoolHits,
            [StringConstants.FIELD_CONNECTION_POOL_WAITS]: this.connectionPoolWaits
        };
    }
}
//# sourceMappingURL=metrics.js.map