import { EnhancedSecurityValidator } from '../src/security.js';
import { DefaultConfig } from '../src/constants.js';

describe('EnhancedSecurityValidator', () => {
  describe('validateInputComprehensive', () => {
    describe('Valid inputs', () => {
      it('should accept valid string inputs', () => {
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('valid string', 'test_field');
        }).not.toThrow();
      });

      it('should accept valid number inputs', () => {
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive(123, 'test_field');
        }).not.toThrow();

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive(45.67, 'test_field');
        }).not.toThrow();
      });

      it('should accept valid boolean inputs', () => {
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive(true, 'test_field');
        }).not.toThrow();

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive(false, 'test_field');
        }).not.toThrow();
      });

      it('should accept null and undefined inputs', () => {
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive(null, 'test_field');
        }).not.toThrow();

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive(undefined, 'test_field');
        }).not.toThrow();
      });

      it('should accept normal SQL queries', () => {
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('SELECT id, name FROM users WHERE active = 1', 'query');
        }).not.toThrow();
      });

      it('should accept table names', () => {
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('users', 'table_name');
        }).not.toThrow();

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('user_profiles', 'table_name');
        }).not.toThrow();

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('table123', 'table_name');
        }).not.toThrow();
      });
    });

    describe('Invalid inputs - Type validation', () => {
      it('should reject invalid data types', () => {
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive({}, 'test_field');
        }).toThrow('test_field 具有无效的数据类型');

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive([], 'test_field');
        }).toThrow('test_field 具有无效的数据类型');

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive(() => {}, 'test_field');
        }).toThrow('test_field 具有无效的数据类型');
      });
    });

    describe('Invalid inputs - String validation', () => {
      it('should reject strings with control characters', () => {
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('test\x00string', 'test_field');
        }).toThrow('test_field 包含无效的控制字符');

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('test\x01string', 'test_field');
        }).toThrow('test_field 包含无效的控制字符');
      });

      it('should allow valid control characters (tab, newline, carriage return)', () => {
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('test\tstring', 'test_field');
        }).not.toThrow();

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('test\nstring', 'test_field');
        }).not.toThrow();

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('test\rstring', 'test_field');
        }).not.toThrow();
      });

      it('should reject strings that exceed maximum length', () => {
        const longString = 'a'.repeat(DefaultConfig.MAX_INPUT_LENGTH + 1);
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive(longString, 'test_field');
        }).toThrow('test_field 超过最大长度限制');
      });
    });

    describe('Security pattern validation - Strict level', () => {
      it('should reject dangerous SQL patterns', () => {
        // File access patterns
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('SELECT LOAD_FILE("/etc/passwd")', 'query');
        }).toThrow('query 包含潜在危险内容');

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('SELECT * INTO OUTFILE "/tmp/dump.txt"', 'query');
        }).toThrow('query 包含潜在危险内容');

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('SELECT * INTO DUMPFILE "/tmp/dump.txt"', 'query');
        }).toThrow('query 包含潜在危险内容');

        // Command execution patterns
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('EXEC xp_cmdshell "dir"', 'query');
        }).toThrow('query 包含潜在危险内容');

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('SYSTEM("rm -rf /")', 'query');
        }).toThrow('query 包含潜在危险内容');

        // Time-based attack patterns
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('SELECT BENCHMARK(1000000, MD5("test"))', 'query');
        }).toThrow('query 包含潜在危险内容');

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('SELECT SLEEP(10)', 'query');
        }).toThrow('query 包含潜在危险内容');

        // System variable access
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('SELECT @@version', 'query');
        }).toThrow('query 包含潜在危险内容');
      });

      it('should reject SQL injection patterns', () => {
        // Quote-based injection with OR conditions
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive("' OR '1'='1", 'input');
        }).toThrow('input 包含潜在的SQL注入尝试');

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('" OR "1"="1', 'input');
        }).toThrow('input 包含潜在的SQL注入尝试');

        // Union-based injection
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive("' UNION SELECT password FROM users --", 'input');
        }).toThrow('input 包含潜在的SQL注入尝试');

        // Boolean-based blind injection
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive("' AND 1=1 --", 'input');
        }).toThrow('input 包含潜在的SQL注入尝试');

        // Classic authentication bypass
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive("admin' --", 'input');
        }).toThrow('input 包含潜在的SQL注入尝试');
      });
    });

    describe('Security pattern validation - Moderate level', () => {
      it('should only check critical patterns in moderate mode', () => {
        // Should still catch file access (critical)
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('SELECT LOAD_FILE("/etc/passwd")', 'query', 'moderate');
        }).toThrow('query 包含危险内容');

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('SELECT * INTO OUTFILE "/tmp/dump"', 'query', 'moderate');
        }).toThrow('query 包含危险内容');

        // Should still catch command execution (critical)
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('EXEC xp_cmdshell "dir"', 'query', 'moderate');
        }).toThrow('query 包含危险内容');

        // May not catch less critical patterns in moderate mode
        // (depending on which patterns are in the first 3 critical patterns)
      });
    });

    describe('Security pattern validation - Basic level', () => {
      it('should skip pattern validation in basic mode', () => {
        // Basic mode should skip pattern validation for performance
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('SELECT LOAD_FILE("/etc/passwd")', 'query', 'basic');
        }).not.toThrow();

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive("' OR '1'='1", 'input', 'basic');
        }).not.toThrow();
      });

      it('should still perform basic validations in basic mode', () => {
        // Should still check control characters
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('test\x00string', 'test_field', 'basic');
        }).toThrow('test_field 包含无效的控制字符');

        // Should still check length
        const longString = 'a'.repeat(DefaultConfig.MAX_INPUT_LENGTH + 1);
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive(longString, 'test_field', 'basic');
        }).toThrow('test_field 超过最大长度限制');
      });
    });

    describe('Edge cases', () => {
      it('should handle empty strings', () => {
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('', 'test_field');
        }).not.toThrow();
      });

      it('should handle strings with only whitespace', () => {
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('   ', 'test_field');
        }).not.toThrow();

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('\t\n\r', 'test_field');
        }).not.toThrow();
      });

      it('should handle strings with special characters', () => {
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('<EMAIL>', 'email');
        }).not.toThrow();

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('price: $19.99', 'description');
        }).not.toThrow();

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('Hello, world! How are you?', 'message');
        }).not.toThrow();
      });

      it('should handle Unicode characters', () => {
        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('测试字符串', 'chinese_text');
        }).not.toThrow();

        expect(() => {
          EnhancedSecurityValidator.validateInputComprehensive('émojis: 😀🎉', 'unicode_text');
        }).not.toThrow();
      });
    });
  });
});