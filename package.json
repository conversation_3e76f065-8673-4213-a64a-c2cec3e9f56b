{"name": "mysql-mcp-server", "version": "1.0.0", "description": "MySQL MCP server for database operations - TypeScript implementation", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "node --loader ts-node/esm src/index.ts", "lint": "eslint src/**/*.ts tests/**/*.ts", "lint:fix": "eslint src/**/*.ts tests/**/*.ts --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/*.test.ts", "test:integration": "jest tests/*.integration.test.ts"}, "keywords": ["mysql", "mcp", "fastmcp", "database"], "author": "liyq", "license": "MIT", "dependencies": {"dotenv": "^16.4.0", "fastmcp": "^2.0.0", "mysql2": "^3.9.0", "zod": "^3.22.0"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.11.0", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "eslint": "^9.32.0", "jest": "^29.7.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.3.0", "typescript-eslint": "^8.39.0"}}