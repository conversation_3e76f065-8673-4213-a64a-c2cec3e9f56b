/**
 * 配置管理系统
 *
 * MySQL MCP 服务器的综合配置管理，包括数据库连接设置、
 * 安全策略和缓存参数。支持环境变量覆盖并提供安全的默认值。
 *
 * @fileoverview 配置接口和管理类
 * <AUTHOR>
 * @since 1.0.0
 */

import { DefaultConfig, StringConstants } from './constants.js';

/**
 * 数据库配置接口
 *
 * 定义所有数据库连接和连接池配置参数。
 * 支持 MySQL 连接设置，包括 SSL、超时和连接池选项。
 *
 * @interface DatabaseConfig
 * @since 1.0.0
 */
export interface DatabaseConfig {
  /** MySQL 服务器主机名或 IP 地址 */
  host: string;

  /** MySQL 服务器端口号（默认：3306） */
  port: number;

  /** 用于身份验证的数据库用户名 */
  user: string;

  /** 用于身份验证的数据库密码 */
  password: string;

  /** 目标数据库名称 */
  database: string;

  /** 连接池中的最大连接数 */
  connectionLimit: number;

  /** 连接池中要维护的最小连接数 */
  minConnections: number;

  /** 连接超时时间（秒） */
  connectTimeout: number;

  /** 空闲连接超时时间（秒） */
  idleTimeout: number;

  /** 是否启用 SSL/TLS 加密 */
  sslEnabled: boolean;
}

/**
 * 安全配置接口
 *
 * 定义查询执行和访问控制的安全策略和限制。
 * 包括速率限制、查询限制和结果集限制。
 *
 * @interface SecurityConfig
 * @since 1.0.0
 */
export interface SecurityConfig {
  /** SQL 查询的最大允许长度 */
  maxQueryLength: number;

  /** 允许的 SQL 查询类型列表（SELECT、INSERT 等） */
  allowedQueryTypes: string[];

  /** 单个查询可以返回的最大行数 */
  maxResultRows: number;

  /** 查询执行超时时间（秒） */
  queryTimeout: number;

  /** 速率限制窗口内的最大请求数 */
  rateLimitMax: number;

  /** 速率限制窗口持续时间（秒） */
  rateLimitWindow: number;
}

/**
 * 缓存配置接口
 *
 * 定义不同类型数据库元数据的缓存参数。
 * 控制缓存大小和生存时间设置以获得最佳性能。
 *
 * @interface CacheConfig
 * @since 1.0.0
 */
export interface CacheConfig {
  /** 要缓存的模式条目的最大数量 */
  schemaCacheSize: number;

  /** 要缓存的表存在性检查的最大数量 */
  tableExistsCacheSize: number;

  /** 要缓存的索引信息条目的最大数量 */
  indexCacheSize: number;

  /** 缓存生存时间（秒） */
  cacheTTL: number;
}

/**
 * 配置管理器类
 *
 * 中央配置管理类，从环境变量加载和验证所有配置设置，
 * 并提供安全的默认值。提供对数据库、安全和缓存配置的类型安全访问。
 *
 * @class ConfigurationManager
 * @since 1.0.0
 */
export class ConfigurationManager {
  /** 数据库连接和连接池配置 */
  public database: DatabaseConfig;

  /** 安全策略和访问控制配置 */
  public security: SecurityConfig;

  /** 缓存系统配置 */
  public cache: CacheConfig;

  /**
   * 配置管理器构造函数
   *
   * 通过从环境变量加载来初始化所有配置部分，
   * 并回退到安全的默认值。
   *
   * @constructor
   */
  constructor() {
    this.database = this.loadDatabaseConfig();
    this.security = this.loadSecurityConfig();
    this.cache = this.loadCacheConfig();
  }

  /**
   * 加载数据库配置
   *
   * 从环境变量加载数据库连接设置，包含验证和安全默认值。
   * 支持MySQL连接参数，包括SSL、超时和连接池。
   *
   * @private
   * @returns {DatabaseConfig} 已验证的数据库配置对象
   */
  private loadDatabaseConfig(): DatabaseConfig {
    return {
      host: process.env[StringConstants.ENV_MYSQL_HOST] || StringConstants.DEFAULT_HOST,
      port: parseInt(process.env[StringConstants.ENV_MYSQL_PORT] || DefaultConfig.MYSQL_PORT.toString(), 10),
      user: process.env[StringConstants.ENV_MYSQL_USER] || StringConstants.DEFAULT_USER,
      password: process.env[StringConstants.ENV_MYSQL_PASSWORD] || StringConstants.DEFAULT_PASSWORD,
      database: process.env[StringConstants.ENV_MYSQL_DATABASE] || StringConstants.DEFAULT_DATABASE,
      connectionLimit: parseInt(process.env[StringConstants.ENV_CONNECTION_LIMIT] || DefaultConfig.CONNECTION_LIMIT.toString(), 10),
      minConnections: DefaultConfig.MIN_CONNECTIONS,
      connectTimeout: parseInt(process.env[StringConstants.ENV_CONNECT_TIMEOUT] || DefaultConfig.CONNECT_TIMEOUT.toString(), 10),
      idleTimeout: parseInt(process.env[StringConstants.ENV_IDLE_TIMEOUT] || DefaultConfig.IDLE_TIMEOUT.toString(), 10),
      sslEnabled: (process.env[StringConstants.ENV_MYSQL_SSL] || '').toLowerCase() === StringConstants.TRUE_STRING
    };
  }

  /**
   * 加载安全配置
   *
   * 从环境变量加载安全策略，包括查询限制、
   * 速率限制和访问控制设置。
   *
   * @private
   * @returns {SecurityConfig} 已验证的安全配置对象
   */
  private loadSecurityConfig(): SecurityConfig {
    const allowedTypesStr = process.env[StringConstants.ENV_ALLOWED_QUERY_TYPES] || StringConstants.DEFAULT_ALLOWED_QUERY_TYPES;
    const allowedTypes = allowedTypesStr.split(',').map(t => t.trim().toUpperCase());

    return {
      maxQueryLength: parseInt(process.env[StringConstants.ENV_MAX_QUERY_LENGTH] || DefaultConfig.MAX_QUERY_LENGTH.toString(), 10),
      allowedQueryTypes: allowedTypes,
      maxResultRows: parseInt(process.env[StringConstants.ENV_MAX_RESULT_ROWS] || DefaultConfig.MAX_RESULT_ROWS.toString(), 10),
      queryTimeout: parseInt(process.env[StringConstants.ENV_QUERY_TIMEOUT] || DefaultConfig.QUERY_TIMEOUT.toString(), 10),
      rateLimitMax: parseInt(process.env[StringConstants.ENV_RATE_LIMIT_MAX] || DefaultConfig.RATE_LIMIT_MAX.toString(), 10),
      rateLimitWindow: parseInt(process.env[StringConstants.ENV_RATE_LIMIT_WINDOW] || DefaultConfig.RATE_LIMIT_WINDOW.toString(), 10)
    };
  }

  /**
   * 加载缓存配置
   *
   * 从环境变量加载缓存系统参数，包括缓存大小和
   * 生存时间设置以获得最佳性能。
   *
   * @private
   * @returns {CacheConfig} 已验证的缓存配置对象
   */
  private loadCacheConfig(): CacheConfig {
    return {
      schemaCacheSize: parseInt(process.env.SCHEMA_CACHE_SIZE || DefaultConfig.SCHEMA_CACHE_SIZE.toString(), 10),
      tableExistsCacheSize: parseInt(process.env.TABLE_EXISTS_CACHE_SIZE || DefaultConfig.TABLE_EXISTS_CACHE_SIZE.toString(), 10),
      indexCacheSize: parseInt(process.env.INDEX_CACHE_SIZE || DefaultConfig.INDEX_CACHE_SIZE.toString(), 10),
      cacheTTL: parseInt(process.env.CACHE_TTL || DefaultConfig.CACHE_TTL.toString(), 10)
    };
  }

  /**
   * 导出配置用于诊断
   *
   * 返回适用于诊断和日志记录的清理配置对象。
   * 敏感信息如密码会被掩码以确保安全。
   *
   * @public
   * @returns {object} 清理后的配置对象
   *
   * @example
   * const config = manager.toObject();
   * console.log(JSON.stringify(config, null, 2));
   */
  public toObject(): object {
    const configObj = {
      database: { ...this.database },
      security: { ...this.security },
      cache: { ...this.cache }
    };

    // 为安全起见掩码敏感信息
    configObj.database.password = '***';

    return configObj;
  }

  /**
   * 获取配置摘要
   *
   * 返回关键配置参数的简洁摘要字符串，
   * 用于快速状态检查和监控仪表板。
   *
   * @public
   * @returns {Record<string, string>} 关键配置参数的字符串形式
   *
   * @example
   * const summary = manager.getSummary();
   * console.log(`数据库: ${summary.database_host}:${summary.database_port}`);
   */
  public getSummary(): Record<string, string> {
    return {
      database_host: this.database.host,
      database_port: this.database.port.toString(),
      connection_limit: this.database.connectionLimit.toString(),
      max_result_rows: this.security.maxResultRows.toString(),
      rate_limit_max: this.security.rateLimitMax.toString(),
      schema_cache_size: this.cache.schemaCacheSize.toString()
    };
  }
}